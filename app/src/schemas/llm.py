from pydantic import BaseModel, Field


class ChatbotRequest(BaseModel):
    """Model for chatbot request parameters."""

    question: str = Field(..., description="User's question")
    model: str = Field(default="gpt-4o-mini", description="LLM model to use")
    temperature: float = Field(default=0.0, description="Temperature parameter", ge=0.0, le=1.0)
    max_tokens: int = Field(default=4096, description="Maximum tokens in response", gt=0, le=4096)
