"""
Google Search Ads Excel formatter.

This module handles Excel file generation for Google Search advertising campaigns.
"""

from typing import Any, Dict

from ..base_writer import BaseExcelWriter
from ..styles import ExcelStyles, set_cell


class GoogleSearchFormatter(BaseExcelWriter):
    """
    Excel formatter for Google Search advertising campaigns.
    """
    
    async def write_excel_file(
        self,
        campaign_data: Dict[str, Any],
        output_format: Dict[str, Any],
        file_path: str
    ) -> None:
        """
        Write Excel file for Google Search advertising campaigns.

        Args:
            campaign_data: Campaign data dictionary
            output_format: Output format configuration
            file_path: Path to save the Excel file
        """
        wb, ws = self.create_workbook("検索広告フォーマット")

        data = campaign_data.get('data', {})
        platform = self.get_field_value(data.get("媒体", "Google"))

        # Table 1: Main campaign details
        table1_headers = ["媒体", "キャンペーン", "広告グループ", "メインキーワード", "掛合わせ1", "マッチタイプ", "広告"]
        for idx, header in enumerate(table1_headers, start=1):
            set_cell(ws, 1, idx, header, fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)

        # Table 1: Non-list fields
        non_list_fields = ["媒体", "キャンペーン"]
        for idx, header in enumerate(table1_headers, start=1):
            if header in non_list_fields:
                ws.merge_cells(start_row=2, start_column=idx, end_row=3, end_column=idx)
                value = self.get_field_value(data.get(header, "Google" if header == "媒体" else ""))
                if isinstance(value, list):
                    value = value[0] if value else ""
                set_cell(ws, 2, idx, value)

        # Table 1: List fields (広告グループ, メインキーワード, 掛合わせ1, マッチタイプ, 広告)
        list_fields = ["広告グループ", "メインキーワード", "掛合わせ1", "マッチタイプ", "広告"]
        max_items = max([len(self.get_field_value(data.get(field, []))) for field in list_fields], default=0)
        max_items = min(max_items, 2)  # Limit to 2 rows
        for idx, header in enumerate(table1_headers, start=1):
            if header in list_fields:
                values = self.get_field_value(data.get(header, []))
                for row, value in enumerate(values[:max_items], start=2):
                    set_cell(ws, row, idx, value)

        # Table 2: Ad details
        base_row = 1
        table2_headers = ["広告名", "見出し(30字以内)", "固定", "文字数", "説明文(90字以内)", "固定", "文字数", "パス", "文字数"]
        for idx, header in enumerate(table2_headers, start=10):
            set_cell(ws, 1, idx, header, fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)

        # Headlines (up to 15)
        headlines = self.get_field_value(data.get("見出し", []))
        current_row = base_row + 1
        for idx, headline in enumerate(headlines[:15]):  # Limit to 15 headlines
            set_cell(ws, current_row + idx, 11, headline)
            set_cell(ws, current_row + idx, 13, str(len(headline)))

        # Descriptions (up to 4)
        descriptions = self.get_field_value(data.get("説明文", []))
        for idx, description in enumerate(descriptions[:4]):  # Limit to 4 descriptions
            row = current_row + idx
            set_cell(ws, row, 14, description)
            set_cell(ws, row, 16, str(len(description)))

        # Paths (up to 2)
        paths = self.get_field_value(data.get("パス", []))
        for idx, path in enumerate(paths[:2]):  # Limit to 2 paths
            row = current_row + idx
            set_cell(ws, row, 17, path)
            set_cell(ws, row, 18, str(len(path)))

        # Table 3: URL and additional info
        table3_row = max(len(headlines[:15]), len(descriptions[:4]), len(paths[:2])) + 1
        set_cell(ws, table3_row, 10, "URL")
        url_value = self.get_field_value(data.get("入稿先URL", ""))
        if platform == "Google":
            set_cell(ws, table3_row + 1, 10, "入稿先URL:Google")
            set_cell(ws, table3_row + 1, 11, url_value + "?utm_source=google&utm_medium=cpc")
        elif platform == "Yahoo":
            set_cell(ws, table3_row + 1, 10, "入稿先URL:Yahoo")
            set_cell(ws, table3_row + 1, 11, url_value + "?utm_source=yahoo&utm_medium=cpc")
        else:
            raise ValueError("Invalid platform specified. Must be 'Google' or 'Yahoo'.")

        # Additional info section
        additional_fields = ["地域", "性別（googleのみ）", "年齢（googleのみ）", "年収（googleのみ）", "ターゲティング", "備考"]
        additional_row = 2 + max_items + 2
        set_cell(ws, additional_row, 1, "＊このキャンペーンの注意事項", fill=ExcelStyles.LIGHT_BLUE_FILL, merge_end_col=7)

        for idx, field in enumerate(additional_fields):
            row = additional_row + 1 + idx
            field_key = field.replace("（googleのみ）", "")  # Extract key without "(googleのみ)"
            value = ""
            if "googleのみ" in field and platform != "Google":
                value = ""  # Keep empty for Yahoo
            else:
                value = self.get_field_value(data.get(field_key, ""))
            set_cell(ws, row, 1, field)
            set_cell(ws, row, 2, value, merge_end_col=7)

        # Auto width (approximate)
        from openpyxl.utils import get_column_letter
        for col in ws.columns:
            max_length = max(len(str(cell.value)) if cell.value else 0 for cell in col)
            ws.column_dimensions[get_column_letter(col[0].column)].width = max_length + 2

        wb.save(file_path)
