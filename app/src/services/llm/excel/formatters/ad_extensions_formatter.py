"""
Ad Extensions Excel formatter.

This module handles Excel file generation for advertising extensions.
"""

from typing import Any, Dict

from ..base_writer import BaseExcelWriter
from ..styles import ExcelStyles, set_cell


class AdExtensionsFormatter(BaseExcelWriter):
    """
    Excel formatter for advertising extensions.
    """
    
    async def write_excel_file(
        self, 
        campaign_data: Dict[str, Any], 
        output_format: Dict[str, Any], 
        file_path: str
    ) -> None:
        """
        Write Excel file for advertising extensions.
        
        Args:
            campaign_data: Campaign data dictionary
            output_format: Output format configuration
            file_path: Path to save the Excel file
        """
        wb, ws = self.create_workbook("広告拡張フォーマット")
        
        data = campaign_data.get('data', {})
        platform = self.get_field_value(data.get("platform_selected", "Google"))
        
        row = 1
        
        # Sitelinks section
        row = self._write_sitelinks_section(ws, data, row)
        
        # Callouts section
        row = self._write_callouts_section(ws, data, row)
        
        # Structured snippets section
        row = self._write_structured_snippets_section(ws, data, row)
        
        # Phone number section
        row = self._write_phone_number_section(ws, data, row)
        
        # Price extensions section (Google only)
        row = self._write_price_extensions_section(ws, data, platform, row)
        
        # Promotion section (Google only)
        row = self._write_promotion_section(ws, data, platform, row)
        
        self.save_workbook(wb, file_path)
    
    def _write_sitelinks_section(self, ws, data: Dict[str, Any], start_row: int) -> int:
        """Write sitelinks section."""
        row = start_row
        
        set_cell(ws, row, 1, "◆サイトリンク／クイックリンク", 
                fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)
        set_cell(ws, row + 1, 1, "※広告とは別にリンク先を付与できます", 
                fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 2, 1, "見出し（最大半角25字）", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 2, 2, "説明文（最大半角35字）", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 2, 3, "URL", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 2, 4, "見出し", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 2, 5, "説明文", fill=ExcelStyles.LIGHT_BLUE_FILL)
        row += 3
        
        sitelinks = self.get_field_value(data.get("サイトリンク", []))
        sitelinks = self.ensure_list(sitelinks, max_items=6)
        
        for idx, sitelink in enumerate(sitelinks):
            sitelink = sitelink if isinstance(sitelink, dict) else {}
            headline = self.get_field_value(sitelink.get("見出し", ""))
            desc1 = self.get_field_value(sitelink.get("説明文1", ""))
            desc2 = self.get_field_value(sitelink.get("説明文2", ""))
            url = self.get_field_value(sitelink.get("URL", ""))
            
            set_cell(ws, row, 1, f"{idx + 1}")
            set_cell(ws, row, 2, headline)
            set_cell(ws, row, 3, desc1)
            set_cell(ws, row, 4, url)
            set_cell(ws, row, 5, str(len(headline)))
            set_cell(ws, row + 1, 3, desc2)
            set_cell(ws, row + 1, 5, str(len(desc2)))
            row += 2
        
        return row + 1
    
    def _write_callouts_section(self, ws, data: Dict[str, Any], start_row: int) -> int:
        """Write callouts section."""
        row = start_row
        
        set_cell(ws, row, 1, "◆コールアウト／テキスト補足", 
                fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)
        set_cell(ws, row + 1, 1, "※広告文に補足のテキストを付与できます", 
                fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 2, 1, "テキスト（最大半角25字）", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 2, 2, "文字数", fill=ExcelStyles.LIGHT_BLUE_FILL)
        row += 3
        
        callouts = self.get_field_value(data.get("コールアウト", []))
        callouts = self.ensure_list(callouts, max_items=6)
        
        for idx, callout in enumerate(callouts):
            set_cell(ws, row, 1, f"{idx + 1}")
            set_cell(ws, row, 2, callout)
            set_cell(ws, row, 3, str(len(callout)))
            row += 1
        
        return row + 1
    
    def _write_structured_snippets_section(self, ws, data: Dict[str, Any], start_row: int) -> int:
        """Write structured snippets section."""
        row = start_row
        
        set_cell(ws, row, 1, "◆構造化スニペット／カテゴリ補足", 
                fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)
        set_cell(ws, row + 1, 1,
                "※商材の詳細情報を記載することが可能となります。また、構造化スニペットは、審査落ちが多いためテキストはお任せいただきたいです。",
                fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 2, 1, "ヘッダー(見出し)", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 2, 2, "テキスト(最大半角25字)", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 2, 3, "文字数", fill=ExcelStyles.LIGHT_BLUE_FILL)
        row += 3
        
        snippet = data.get("構造化スニペット", {})
        header = self.get_field_value(snippet.get("ヘッダー", "スタイル"))
        values = self.get_field_value(snippet.get("値", []))
        values = self.ensure_list(values, max_items=10)
        
        set_cell(ws, row, 1, "1")
        set_cell(ws, row, 2, header)
        set_cell(ws, row, 3, str(len(header)))
        row += 1
        
        for idx, value in enumerate(values):
            set_cell(ws, row, 1, f"{idx + 2}")
            set_cell(ws, row, 2, value)
            set_cell(ws, row, 3, str(len(value)))
            row += 1
        
        return row + 1
    
    def _write_phone_number_section(self, ws, data: Dict[str, Any], start_row: int) -> int:
        """Write phone number section."""
        row = start_row
        
        set_cell(ws, row, 1, "◆電話番号表示", 
                fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)
        set_cell(ws, row + 1, 1, "※広告表示の際に電話番号を付与することが可能", 
                fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 2, 1, "電話番号", fill=ExcelStyles.LIGHT_BLUE_FILL)
        row += 3
        
        phone = self.get_field_value(data.get("電話番号表示", ""))
        set_cell(ws, row, 1, phone)
        row += 1
        
        return row + 1

    def _write_price_extensions_section(self, ws, data: Dict[str, Any], platform: str, start_row: int) -> int:
        """Write price extensions section (Google only)."""
        row = start_row

        set_cell(ws, row, 1, "◆価格表示オプション（Googleのみ）",
                fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)
        set_cell(ws, row + 1, 1, "※価格表示の詳細を記載することが可能",
                fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 2, 1, "価格表示オプション", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 2, 2, "文字数", fill=ExcelStyles.LIGHT_BLUE_FILL)
        row += 3

        if platform == "Google":
            price_extensions = self.get_field_value(data.get("価格表示オプション", []))
            price_extensions = self.ensure_list(price_extensions, max_items=3)

            for idx, price in enumerate(price_extensions):
                price = price if isinstance(price, dict) else {}
                header = self.get_field_value(price.get("ヘッダー", ""))
                desc = self.get_field_value(price.get("説明文", ""))
                price_value = self.get_field_value(price.get("価格", ""))
                unit = self.get_field_value(price.get("単位", "単位なし"))
                url = self.get_field_value(price.get("URL", ""))

                set_cell(ws, row, 1, f"{idx + 1}")
                set_cell(ws, row, 2, "ヘッダー（最大半角25字）")
                set_cell(ws, row, 3, header)
                set_cell(ws, row, 4, str(len(header)))
                set_cell(ws, row + 1, 2, "価格")
                set_cell(ws, row + 1, 3, price_value)
                set_cell(ws, row + 1, 4, unit)
                set_cell(ws, row + 2, 2, "説明文（最大半角25字）")
                set_cell(ws, row + 2, 3, desc)
                set_cell(ws, row + 2, 4, str(len(desc)))
                set_cell(ws, row + 3, 2, "URL")
                set_cell(ws, row + 3, 3, url)
                set_cell(ws, row + 3, 4, str(len(url)))
                row += 4
        else:  # Yahoo: Keep headers but empty content
            for idx in range(3):
                set_cell(ws, row, 1, f"{idx + 1}")
                set_cell(ws, row, 2, "ヘッダー（最大半角25字）")
                set_cell(ws, row, 3, "")
                set_cell(ws, row, 4, "0")
                set_cell(ws, row + 1, 2, "価格")
                set_cell(ws, row + 1, 3, "")
                set_cell(ws, row + 1, 4, "単位なし")
                set_cell(ws, row + 2, 2, "説明文（最大半角25字）")
                set_cell(ws, row + 2, 3, "")
                set_cell(ws, row + 2, 4, "0")
                set_cell(ws, row + 3, 2, "URL")
                set_cell(ws, row + 3, 3, "")
                set_cell(ws, row + 3, 4, "0")
                row += 4

        return row + 1

    def _write_promotion_section(self, ws, data: Dict[str, Any], platform: str, start_row: int) -> int:
        """Write promotion section (Google only)."""
        row = start_row

        set_cell(ws, row, 1, "◆プロモーション（Googleのみ）",
                fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)
        row += 1

        promotion = data.get("プロモーション", {}) if platform == "Google" else {}
        promotion_fields = [
            {"key": "年間行事", "label": "年間行事", "default": "なし"},
            {"key": "プロモーションタイプ", "label": "プロモーションタイプ", "suffix": "割引額 ￥"},
            {"key": "アイテム", "label": "アイテム", "default": ""},
            {"key": "最終ページURL", "label": "最終ページURL", "default": ""},
            {"key": "プロモーションの詳細", "label": "プロモーションの詳細", "default": "なし"},
            {"key": "開始日", "label": "開始日", "default": ""},
            {"key": "終了日", "label": "終了日", "default": ""}
        ]

        for field in promotion_fields:
            value = self.get_field_value(promotion.get(field["key"], field.get("default", "")))
            if platform != "Google" and field["key"] != "年間行事":
                value = ""
            set_cell(ws, row, 1, field["label"])
            suffix = f" {field['suffix']}" if "suffix" in field else ""
            set_cell(ws, row, 2, value + suffix)
            row += 1

        # Schedule details
        schedule = promotion.get("スケジュールの詳細", {}) if platform == "Google" else {}
        schedule_fields = [
            {"key": "開始日", "label": "開始日", "default": ""},
            {"key": "終了日", "label": "終了日", "default": ""},
            {"key": "曜日と時間帯", "label": "曜日と時間帯", "default": ""}
        ]

        set_cell(ws, row, 1, "スケジュールの詳細")
        row += 1

        for field in schedule_fields:
            value = self.get_field_value(schedule.get(field["key"], field.get("default", "")))
            if platform != "Google":
                value = ""
            set_cell(ws, row, 1, field["label"])
            set_cell(ws, row, 2, value)
            row += 1

        return row
