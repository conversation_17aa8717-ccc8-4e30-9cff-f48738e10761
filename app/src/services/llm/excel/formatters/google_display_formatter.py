"""
Google Display Ads Excel formatter.

This module handles Excel file generation for Google Display advertising campaigns.
"""

from typing import Any, Dict

from ..base_writer import BaseExcelWriter
from ..styles import ExcelStyles, set_cell


class GoogleDisplayFormatter(BaseExcelWriter):
    """
    Excel formatter for Google Display advertising campaigns.
    """
    
    async def write_excel_file(
        self, 
        campaign_data: Dict[str, Any], 
        output_format: Dict[str, Any], 
        file_path: str
    ) -> None:
        """
        Write Excel file for Google Display advertising campaigns.
        
        Args:
            campaign_data: Campaign data dictionary
            output_format: Output format configuration
            file_path: Path to save the Excel file
        """
        wb, ws = self.create_workbook("Googleディスプレイ広告")
        
        # Headers
        headers = [
            "媒体", "キャンペーン名", "広告グループ名", "配信条件", 
            "デバイス", "性別", "年齢", "エリア", "除外プレースメント"
        ]
        
        for idx, header in enumerate(headers, start=1):
            set_cell(ws, 1, idx, header, fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)
        
        data = campaign_data.get('data', {})

        # Write non-list fields
        non_list_fields = ["媒体", "キャンペーン名", "デバイス", "性別", "年齢", "エリア", "除外プレースメント"]
        for idx, header in enumerate(headers, start=1):
            if header in non_list_fields:
                ws.merge_cells(start_row=2, start_column=idx, end_row=3, end_column=idx)
                value = self.get_field_value(data.get(header, "Googleディスプレイ" if header == "媒体" else ""))
                set_cell(ws, 2, idx, value)

        # Write list fields
        list_fields = {"広告グループ名": 2, "配信条件": 2}
        for idx, header in enumerate(headers, start=1):
            if header in list_fields:
                values = self.get_field_value(data.get(header, []))
                for row, value in enumerate(values, start=2):
                    set_cell(ws, row, idx, value)

        # Link destination
        set_cell(ws, 4, 1, "リンク先", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 4, 2, "", merge_end_col=9)

        # Get platform
        platform = self.get_field_value(data.get("媒体", data.get("platform_selected", "Googleディスプレイ")))

        # URL section based on platform
        base_row = 5
        row = base_row
        if platform == "Googleディスプレイ":
            google_url = self.get_field_value(
                data.get("広告文", {}).get("Google", {}).get("URL", "?utm_source=google&utm_medium=display"))
            set_cell(ws, row, 1, "Google URL", fill=ExcelStyles.LIGHT_BLUE_FILL)
            set_cell(ws, row, 2, google_url, merge_end_col=9)
            row += 1
        elif platform == "Yahooディスプレイ":
            yda_url = self.get_field_value(
                data.get("広告文", {}).get("Yahoo", {}).get("URL", "?utm_source=yahoo&utm_medium=display"))
            set_cell(ws, row, 1, "YDA URL", fill=ExcelStyles.LIGHT_BLUE_FILL)
            set_cell(ws, row, 2, yda_url, merge_end_col=9)
            row += 1
        else:
            raise ValueError("Invalid platform specified. Must be 'Googleディスプレイ' or 'Yahooディスプレイ'.")

        # Skip a row
        row += 1

        # Ad content based on platform
        if platform == "Yahooディスプレイ":
            self._write_yahoo_display_ads(ws, data, row)
        elif platform == "Googleディスプレイ":
            self._write_google_display_ads(ws, data, row)

        # Auto width (approximate)
        from openpyxl.utils import get_column_letter
        for col in ws.columns:
            max_length = max(len(str(cell.value)) if cell.value else 0 for cell in col)
            ws.column_dimensions[get_column_letter(col[0].column)].width = max_length + 2

        wb.save(file_path)

    def _write_yahoo_display_ads(self, ws, data: Dict[str, Any], start_row: int) -> None:
        """Write Yahoo display ads section."""
        row = start_row

        # Yahoo display ad text
        set_cell(ws, row, 1, "広告文（Yahooディスプレイ）")
        set_cell(ws, row + 1, 1, "広告種類", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 1, 2, "配信内容", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 1, 3, "広告文", merge_end_col=7, fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 1, 8, "文字数", fill=ExcelStyles.LIGHT_BLUE_FILL)
        row += 2

        # Yahoo text ads
        yahoo_text = data.get("広告文", {}).get("Yahoo", {}).get("テキスト広告", {})
        text_ad_configs = [
            {"key": "タイトル", "label": "タイトル（最大30文字）", "max_chars": 30},
            {"key": "説明文1", "label": "説明文１（最大38文字）", "max_chars": 38},
            {"key": "説明文2", "label": "説明文２（最大38文字）", "max_chars": 38}
        ]

        for config in text_ad_configs:
            value = self.get_field_value(yahoo_text.get(config["key"], ""))
            set_cell(ws, row, 2, config["label"])
            set_cell(ws, row, 3, value, merge_end_col=7)
            set_cell(ws, row, 8, len(value) if value else 0)
            row += 1
        set_cell(ws, start_row + 2, 1, "テキスト広告", merge_end_row=row-1)

        # Yahoo responsive ads
        yahoo_resp = data.get("広告文", {}).get("Yahoo", {}).get("レスポンシブ広告", {})
        resp_ad_configs = [
            {"key": "主体者表記", "label": "主体者表記（最大40文字）", "max_chars": 40, "is_list": False},
            {"key": "タイトル", "label_base": "タイトル{num} 通常（最大40文字）", "max_chars": 40, "is_list": True},
            {"key": "説明文", "label_base": "説明文{num}（最大90文字）", "max_chars": 90, "is_list": True}
        ]

        for config in resp_ad_configs:
            values = self.get_field_value(yahoo_resp.get(config["key"], [] if config["is_list"] else ""))
            if not config["is_list"]:
                values = [values]
            for idx, value in enumerate(values):
                label = config["label"] if "label" in config else config["label_base"].format(num=idx + 1)
                set_cell(ws, row, 2, label)
                set_cell(ws, row, 3, value, merge_end_col=7)
                set_cell(ws, row, 8, len(value) if value else 0)
                row += 1
        set_cell(ws, start_row + 2 + len(text_ad_configs), 1, "レスポンシブ広告", merge_end_row=row-1)

    def _write_google_display_ads(self, ws, data: Dict[str, Any], start_row: int) -> None:
        """Write Google display ads section."""
        row = start_row

        # Google display ad text
        set_cell(ws, row, 1, "広告文（Googleディスプレイ）")
        set_cell(ws, row + 1, 1, "広告種類", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 1, 2, "配信内容", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 1, 3, "広告文", merge_end_col=7, fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 1, 8, "文字数", fill=ExcelStyles.LIGHT_BLUE_FILL)
        row += 2

        google_ad = data.get("広告文", {}).get("Google", {})
        google_ad_configs = [
            {"key": "主体者表記", "label": "主体者表記（最大25文字）", "max_chars": 25, "is_list": False},
            {"key": "広告見出し", "label": "広告見出し 通常（最大90文字）", "max_chars": 90, "is_list": False},
            {"key": "広告見出し 短縮", "label_base": "広告見出し 短縮（最大30文字）", "max_chars": 30, "is_list": True},
            {"key": "説明文", "label_base": "説明文（最大90文字）", "max_chars": 90, "is_list": True}
        ]

        for config in google_ad_configs:
            values = self.get_field_value(google_ad.get(config["key"], [] if config["is_list"] else ""))
            if not config["is_list"]:
                values = [values]
            for idx, value in enumerate(values):
                label = config["label"] if "label" in config else config["label_base"] + ("【任意】" if idx > 0 else "")
                set_cell(ws, row, 2, label)
                set_cell(ws, row, 3, value, merge_end_col=7)
                set_cell(ws, row, 8, len(value) if value else 0)
                row += 1
        set_cell(ws, start_row + 2, 1, "レスポンシブ ディスプレイ広告", merge_end_row=row-1)
